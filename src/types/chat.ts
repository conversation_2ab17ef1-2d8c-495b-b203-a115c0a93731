export interface ChatMessage {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: Date
}

export interface ChatRequest {
  messages: Omit<ChatMessage, "id" | "timestamp">[]
  model?: string
  temperature?: number
  max_tokens?: number
}

export interface ChatResponse {
  content: string
  error?: string
}

export interface ChatState {
  messages: ChatMessage[]
  isLoading: boolean
  error: string | null
}

export type MessageRole = ChatMessage["role"]

// 用于流式响应的事件类型
export interface StreamChunk {
  content: string
}

// 聊天配置
export interface ChatConfig {
  model: string
  temperature: number
  max_tokens: number
}

// 默认配置
export const DEFAULT_CHAT_CONFIG: ChatConfig = {
  model: "bedrock-claude-4-sonnet",
  temperature: 0.7,
  max_tokens: 2048,
}
