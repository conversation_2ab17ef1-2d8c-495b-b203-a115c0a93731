import { MessagePreprocessor, ResponsePostprocessor } from "@/types/chat"

/**
 * Default message preprocessor - returns message as is
 */
export const defaultMessagePreprocessor: MessagePreprocessor = (
  message: string,
): string => {
  return message
}

/**
 * Default response postprocessor - extracts JSON output field if present
 */
export const defaultResponsePostprocessor: ResponsePostprocessor = (
  response: string,
): string => {
  try {
    // Try to find JSO<PERSON> in the response
    const jsonMatch = response.match(/\{[\s\S]*\}/)

    if (jsonMatch) {
      const jsonStr = jsonMatch[0]
      const parsed = JSON.parse(jsonStr)

      // Log the parsed JSON for debugging
      console.log("Detected JSON in response:", parsed)

      // If there's an output field, return it; otherwise return original response
      if (parsed.output && typeof parsed.output === "string") {
        return parsed.output
      }
    }

    // If no JSON found or no output field, return original response
    return response
  } catch (error) {
    // If <PERSON>SO<PERSON> parsing fails, return original response
    console.warn("Failed to parse JSON from response:", error)
    return response
  }
}

/**
 * Custom preprocessor that trims whitespace and converts to lowercase
 */
export const trimAndLowercasePreprocessor: MessagePreprocessor = (
  message: string,
): string => {
  return message.trim().toLowerCase()
}

/**
 * Custom preprocessor that adds context prefix
 */
export const addContextPreprocessor: MessagePreprocessor = (
  message: string,
): string => {
  return `Context: Please provide a helpful response.\n\nUser: ${message}`
}

/**
 * Custom postprocessor that removes markdown formatting
 */
export const removeMarkdownPostprocessor: ResponsePostprocessor = (
  response: string,
): string => {
  return response
    .replace(/\*\*(.*?)\*\*/g, "$1") // Remove bold
    .replace(/\*(.*?)\*/g, "$1") // Remove italic
    .replace(/`(.*?)`/g, "$1") // Remove inline code
    .replace(/```[\s\S]*?```/g, "") // Remove code blocks
}

/**
 * Registry of available processors
 */
export const MESSAGE_PROCESSORS = {
  preprocessors: {
    default: defaultMessagePreprocessor,
    trimAndLowercase: trimAndLowercasePreprocessor,
    addContext: addContextPreprocessor,
  },
  postprocessors: {
    default: defaultResponsePostprocessor,
    removeMarkdown: removeMarkdownPostprocessor,
  },
} as const

export type PreprocessorKey = keyof typeof MESSAGE_PROCESSORS.preprocessors
export type PostprocessorKey = keyof typeof MESSAGE_PROCESSORS.postprocessors
