{"name": "memorai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts"}, "dependencies": {"@opennextjs/cloudflare": "^1.6.5", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "next": "15.4.6", "openai": "^5.15.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/compat": "^1.3.2", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.34.0", "@next/eslint-plugin-next": "^15.5.0", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^20.19.11", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.8", "eslint": "^9.34.0", "eslint-config-next": "15.4.6", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.3.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2", "wrangler": "^4.32.0"}, "packageManager": "pnpm@10.13.1"}